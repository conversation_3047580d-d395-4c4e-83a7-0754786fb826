docs: https://docs.stripe.com/testing

Successful Payments:
4242 4242 4242 4242    # Visa - Always succeeds
4000 0566 5566 5556    # Visa (3D Secure) - Requires authentication
5555 5555 5555 4444    # Mastercard - Always succeeds
3782 822463 10005      # American Express - Always succeeds

Failed Payments:
4000 0000 0000 0002    # Generic decline
4000 0000 0000 9995    # Insufficient funds decline
4000 0000 0000 9987    # Lost card decline
4000 0000 0000 0069    # Expired card decline
4000 0000 0000 0127    # Incorrect CVC decline

Special Cases:
4000 0027 6000 3184    # Requires verification (will trigger additional security check)
4000 0000 0000 3220    # 3D Secure 2 authentication required
4000 0000 0000 3063    # 3D Secure 2 authentication must be completed


For testing purposes, you can use:
Any future expiration date (e.g., 12/34)
Any 3-digit CVC (e.g., 123)
Any name
Any postal code (e.g., 12345)
For 3D Secure authentication testing:
Use "success" when prompted to complete the authentication
Use "failure" to simulate a failed authentication
For international payments, you can use these test cards with different currencies:
EUR: 4000 0000 0000 0002
GBP: 4000 0000 0000 0002
JPY: 4000 0000 0000 0002