spring:
  datasource:
    url: jdbc:h2:mem:testdb
    username: sa
    password: 
    driver-class-name: org.h2.Driver
  jpa:
    hibernate:
      ddl-auto: create-drop
    database-platform: org.hibernate.dialect.H2Dialect
  flyway:
    enabled: false

# Disable security for tests
jwt:
  secret: test-secret-key-for-testing-purposes-only
  expirationMs: 3600000

# Storage configuration for tests
storage:
  location: target/test-images
  base-url: /test-images
  cleanup-enabled: false
