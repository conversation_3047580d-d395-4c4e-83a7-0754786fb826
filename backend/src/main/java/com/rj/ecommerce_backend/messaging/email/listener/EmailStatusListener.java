package com.rj.ecommerce_backend.messaging.email.listener;

import com.rj.ecommerce.api.shared.messaging.email.EmailDeliveryStatusDTO;
import com.rj.ecommerce_backend.notification.EmailNotificationServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;

import static com.rj.ecommerce_backend.messaging.config.RabbitMQConfig.EMAIL_NOTIFICATION_QUEUE;

@Component
@RequiredArgsConstructor
@Slf4j
public class EmailStatusListener {

    private final EmailNotificationServiceImpl emailNotificationService;

    @RabbitListener(queues = EMAIL_NOTIFICATION_QUEUE)
    public void handleEmailStatus(EmailDeliveryStatusDTO status) {
        log.info("Received email status: {} for message: {}",
                status.getStatus(), status.getOriginalMessageId());

        // Update notification status in the database
        emailNotificationService.updateEmailStatus(
                status.getOriginalMessageId(),
                status.getStatus().name(),
                status.getErrorMessage()
        );
    }
}
