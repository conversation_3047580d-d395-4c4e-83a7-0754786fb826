package com.rj.ecommerce_backend.order.repository

import com.rj.ecommerce_backend.order.domain.Order
import org.springframework.data.domain.Pageable
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.JpaSpecificationExecutor
import org.springframework.data.jpa.repository.Query
import org.springframework.data.repository.query.Param
import org.springframework.stereotype.Repository

@Repository
interface OrderRepository: JpaRepository<Order, Long>,
    JpaSpecificationExecutor<Order> {

    fun findByUserId(userId: Long, pageable: Pageable)

    @Query(value = "SELECT o FROM o LEFT JOIN FETCH o.orderItems WHERE o.id = :orderId AND o.user.id = :userId")
    fun findByIdWithOrderItems(@Param("orderId") orderId: Long, @Param("userId") userId: Long): Order?

}