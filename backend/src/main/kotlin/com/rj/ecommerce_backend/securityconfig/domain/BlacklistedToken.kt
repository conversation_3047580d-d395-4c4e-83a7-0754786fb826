package com.rj.ecommerce_backend.securityconfig.domain

import jakarta.persistence.*
import org.hibernate.annotations.CreationTimestamp
import org.hibernate.annotations.UpdateTimestamp
import org.springframework.data.annotation.CreatedBy
import org.springframework.data.annotation.LastModifiedBy
import org.springframework.data.jpa.domain.support.AuditingEntityListener
import java.time.LocalDateTime

@Entity
@Table(name = "blacklisted_tokens")
@EntityListeners(AuditingEntityListener::class) // Enable Spring Data JPA Auditing
class BlacklistedToken(
    @Column(name = "token_string", length = 500, nullable = false, unique = true)
    var tokenString: String,

    // This is the business timestamp of when the token was deemed blacklisted
    @Column(name = "token_blacklisted_timestamp", nullable = false, updatable = false)
    var tokenBlacklistedTimestamp: LocalDateTime = LocalDateTime.now(), // Renamed for clarity

    @Column(name = "token_expires_at", nullable = false) // When the original token (or this entry) expires
    var tokenExpiresAt: LocalDateTime,

    @Column(name = "user_id_associated_token") // User ID this token originally belonged to
    var userIdAssociatedWithToken: Long?,

    @Column(name = "blacklisting_agent") // Who/what performed the blacklisting action
    var blacklistingAgent: String?
) {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    var id: Long? = null

    // --- Standard Auditing Fields for this BlacklistedToken DB record ---
    @CreationTimestamp // Managed by Hibernate/JPA for DB row creation
    @Column(name = "record_created_at", nullable = false, updatable = false)
    var recordCreatedAt: LocalDateTime? = null

    @UpdateTimestamp // Managed by Hibernate/JPA for DB row update
    @Column(name = "record_updated_at", nullable = false)
    var recordUpdatedAt: LocalDateTime? = null

    @CreatedBy // Managed by Spring Data JPA Auditing
    @Column(name = "record_created_by", updatable = false)
    var recordCreatedBy: String? = null // User who created this blacklist DB record

    @LastModifiedBy // Managed by Spring Data JPA Auditing
    @Column(name = "record_last_modified_by")
    var recordLastModifiedBy: String? = null // User who last modified this blacklist DB record

    // ... equals, hashCode, toString focusing on id or tokenString ...
    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (other == null || javaClass != other.javaClass) return false
        other as BlacklistedToken
        return if (id != null) id == other.id else tokenString == other.tokenString && other.id == null
    }

    override fun hashCode(): Int = id?.hashCode() ?: tokenString.hashCode()

    override fun toString(): String {
        return "BlacklistedToken(id=$id, tokenString='${tokenString.take(8)}...', userIdAssociatedToken=$userIdAssociatedWithToken, tokenBlacklistedTimestamp=$tokenBlacklistedTimestamp)"
    }
}