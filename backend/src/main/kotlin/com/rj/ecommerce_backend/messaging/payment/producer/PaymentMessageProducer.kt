package com.rj.ecommerce_backend.messaging.payment.producer

import com.rj.ecommerce_backend.messaging.common.producer.AbstractMessageProducer
import com.rj.ecommerce_backend.messaging.config.RabbitMQProperties
import org.springframework.amqp.rabbit.core.RabbitTemplate
import org.springframework.stereotype.Component

@Component
class PaymentMessageProducer(
    rabbitTemplate: RabbitTemplate,
    private val rabbitMQProperties: RabbitMQProperties
) : AbstractMessageProducer(rabbitTemplate) {


    fun <T : Any> sendCheckoutSessionRequest(request: T, correlationId: String) {
        val checkoutSessionConfig = rabbitMQProperties.checkoutSession
        sendMessage(
            exchange = checkoutSessionConfig.exchange,
            routingKey = checkoutSessionConfig.routingKey,
            message = request,
            correlationId = correlationId
        )
    }


}