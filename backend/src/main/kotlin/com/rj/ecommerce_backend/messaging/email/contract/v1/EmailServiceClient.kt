package com.rj.ecommerce_backend.messaging.email.contract.v1

import com.rj.ecommerce.api.shared.enums.NotificationEntityType
import com.rj.ecommerce.api.shared.messaging.email.*
import com.rj.ecommerce_backend.messaging.email.producer.EmailMessageProducer
import com.rj.ecommerce_backend.notification.EmailNotificationService // Use the Interface
import io.github.oshai.kotlinlogging.KotlinLogging
import org.springframework.stereotype.Component

@Component
class EmailServiceClient(
    private val emailNotificationService: EmailNotificationService,
    private val emailMessageProducer: EmailMessageProducer
) {
    companion object {
        private val log = KotlinLogging.logger {}
    }

    fun sendEmailRequest(request: EcommerceEmailRequest) {
        log.info { "Processing email request ${request.messageId} to ${request.to} using template ${request.template.name}" }

        val (entityType, entityId) = extractNotificationMetadata(request)

        val creationRequest = NotificationCreationRequest(
            messageId = request.messageId,
            recipient = request.to,
            subject = request.subject,
            template = request.template, // Pass enum, service implementation handles .name
            entityType = entityType,
            entityId = entityId
        )
        val notification = emailNotificationService.createNotification(creationRequest)

        try {
            emailMessageProducer.sendEmail(request, notification.messageId)
            log.info { "Successfully dispatched email request ${notification.messageId} to producer." }
        } catch (e: Exception) {
            val reason = "Failed to dispatch to message queue: ${e.message ?: "Unknown producer error"}"
            log.error(e) { "Error dispatching email ${notification.messageId}. Marking as FAILED. Reason: $reason" }
            emailNotificationService.markAsFailed(notification.messageId, reason)

            // Throw a new, more specific exception that wraps the original cause
            throw MessageDispatchException("Failed to send email request for messageId ${notification.messageId}", e)
        }
    }

    private fun extractNotificationMetadata(request: EcommerceEmailRequest): Pair<NotifiableEntityType, String> {
        return when (request) {
            is OrderEmailRequestDTO -> NotifiableEntityType.ORDER to request.orderId
            is PaymentEmailRequest_V1_DTO -> NotifiableEntityType.PAYMENT to request.orderId.toString()
            is WelcomeEmailRequestDTO -> NotifiableEntityType.CUSTOMER to request.to
            else -> NotifiableEntityType.UNKNOWN to request.messageId
        }
    }
}