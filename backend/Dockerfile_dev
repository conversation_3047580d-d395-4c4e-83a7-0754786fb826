FROM eclipse-temurin:17-jdk
WORKDIR /app
COPY .mvn/ .mvn/
COPY mvnw pom.xml ./
RUN chmod +x mvnw && ./mvnw dependency:go-offline
RUN mkdir -p /app/product-images && chmod 777 /app/product-images
CMD ["./mvnw", "spring-boot:run"]

# Dockerfile_dev
#FROM eclipse-temurin:17-jdk
#
#WORKDIR /app
#
## Copy Maven wrapper and pom.xml
#COPY .mvn/ .mvn/
#COPY mvnw pom.xml ./
#RUN chmod +x mvnw && ./mvnw dependency:go-offline
#
## Copy your application code (this is the crucial missing step)
#COPY src/ ./src/  # Or COPY . .  if your Dockerfile is at the project root
#
## Create product-images directory (better to do this after COPY to avoid permissions issues)
#RUN mkdir -p /app/product-images && chmod 777 /app/product-images
#
#
## Build the application inside the container
#RUN ./mvnw package -DskipTests
#
## Use ENTRYPOINT to set JAVA_OPTS and run the jar
#ENTRYPOINT ["java", "$JAVA_OPTS", "-jar", "target/*.jar"]