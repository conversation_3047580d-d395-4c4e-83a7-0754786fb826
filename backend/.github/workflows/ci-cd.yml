name: Backend CI/CD Pipeline

on:
  pull_request:
    branches: [ "main" ]
  push:
    tags:
      - 'v*'

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  build-and-publish:
    runs-on: ubuntu-latest
    permissions:
      contents: read
      packages: write

    services:
      mysql:
        image: mysql:8.0
        env:
          MYSQL_DATABASE: ecommerce_dev
          MYSQL_USER: testuser
          MYSQL_PASSWORD: ${{ secrets.DATABASE_PASSWORD }}
          MYSQL_ROOT_PASSWORD: ${{ secrets.MYSQL_ROOT_PASSWORD }}
        ports:
          - 3306:3306
        options: >-
          --health-cmd="mysqladmin ping"
          --health-interval=10s
          --health-timeout=5s
          --health-retries=5
    steps:
      - uses: actions/checkout@v4

      - name: Set up JDK 17
        uses: actions/setup-java@v4
        with:
          java-version: '17'
          distribution: 'temurin'
          cache: maven

      - name: Build with Maven
        env:
          SPRING_PROFILES_ACTIVE: ci
#          SPRING_DATASOURCE_URL: *****************************************
#          SPRING_DATASOURCE_USERNAME: testuser
          DATABASE_PASSWORD: ${{ secrets.DATABASE_PASSWORD }}
        #        run: mvn -B package --file pom.xml
        run: mvn clean package --file pom.xml

      - name: Run tests
        env:
          SPRING_PROFILES_ACTIVE: ci
#          SPRING_DATASOURCE_URL: *****************************************
#          SPRING_DATASOURCE_USERNAME: testuser
          DATABASE_PASSWORD: ${{ secrets.DATABASE_PASSWORD }}
        run: mvn test

      - name: Upload test results
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: test-results
          path: target/surefire-reports

      - name: Login to GitHub Container Registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Extract Docker metadata
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}
          tags: |
            type=ref,event=branch
            type=sha,format=long
            type=semver,pattern={{version}}
            type=raw,value=latest,enable={{is_default_branch}}
      - name: Build and push Docker image
        uses: docker/build-push-action@v5
        with:
          context: .
          file: ./Dockerfile_dev
          push: ${{ github.event_name != 'pull_request' }}
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}

  security-scan:
    needs: build-and-publish
    if: github.event_name == 'push' && startsWith(github.ref, 'refs/tags/')
    runs-on: ubuntu-latest
    permissions:
      contents: read
      packages: read
      security-events: write

    env:
      REGISTRY: ghcr.io
      IMAGE_NAME: anielskieoczko/ecommerce_backend

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Login to GitHub Container Registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      # Get the tag name directly from the GitHub ref (removing 'v' prefix if present)
      - name: Get the version
        id: get_version
        run: |
          VERSION=${GITHUB_REF#refs/tags/}
          # Remove 'v' prefix if present
          VERSION=${VERSION#v}
          echo "VERSION=$VERSION" >> $GITHUB_ENV

      # Set the full image reference with correct casing and format
      - name: Set image reference
        run: |
          echo "IMAGE_REF=${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:${{ env.VERSION }}" >> $GITHUB_ENV

      # Pull the image using the exact tag
      - name: Pull image to scan
        run: docker pull ${{ env.IMAGE_REF }}

      # Run Trivy vulnerability scanner
      - name: Run Trivy vulnerability scanner
        uses: aquasecurity/trivy-action@master
        with:
          image-ref: ${{ env.IMAGE_REF }}
          format: 'sarif'
          output: 'trivy-results.sarif'
          severity: 'CRITICAL,HIGH'

      # Upload Trivy scan results to GitHub Security tab
      - name: Upload Trivy scan results to GitHub Security tab
        uses: github/codeql-action/upload-sarif@v2
        with:
          sarif_file: 'trivy-results.sarif'

      # Generate and save HTML report
      - name: Run Trivy vulnerability scanner (HTML report)
        uses: aquasecurity/trivy-action@master
        with:
          image-ref: ${{ env.IMAGE_REF }}
          format: 'table'
          output: 'trivy-results.txt'
          severity: 'CRITICAL,HIGH,MEDIUM'

      # Upload HTML report as artifact
      - name: Upload Trivy scan results
        uses: actions/upload-artifact@v4
        with:
          name: trivy-scan-results
          path: trivy-results.txt
