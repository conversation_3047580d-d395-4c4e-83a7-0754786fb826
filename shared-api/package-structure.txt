com.rj.ecommerce.api.shared
├── core                  # Core value objects and basic types
│   ├── Money.kt
│   ├── Address.kt
│   ├── PhoneNumber.kt
│   └── ImageInfo.kt
├── enums                 # All shared enums
│   ├── OrderStatus.kt
│   ├── PaymentStatus.kt
│   ├── PaymentMethod.kt
│   ├── ShippingMethod.kt
│   ├── EmailTemplate.kt
│   └── EmailStatus.kt
├── dto                   # Common DTOs used across services
│   ├── customer          # Customer-related DTOs
│   │   └── CustomerInfo.kt
│   ├── product           # Product-related DTOs
│   │   ├── ProductSummary.kt
│   │   └── OrderItem.kt
│   ├── order             # Order-related DTOs
│   │   ├── Order.kt
│   │   ├── OrderCreateRequest.kt
│   │   └── OrderStatusUpdateRequest.kt
│   ├── cart              # Cart-related DTOs
│   │   ├── Cart.kt
│   │   ├── CartItem.kt
│   │   └── CartItemAddRequest.kt
│   ├── user              # User-related DTOs
│   │   ├── UserInfo.kt
│   │   ├── UserCreateRequest.kt
│   │   ├── UserUpdateRequest.kt
│   │   ├── ChangePasswordRequest.kt
│   │   └── AuthResponse.kt
│   └── product           # Product-related DTOs
│       ├── Product.kt
│       ├── ProductCreateRequest.kt
│       ├── ProductUpdateRequest.kt
│       └── Category.kt
├── messaging             # Messaging contract DTOs
│   ├── email             # Email-related messaging DTOs
│   │   ├── OrderEmailRequest.kt
│   │   ├── PaymentEmailRequest.kt
│   │   ├── WelcomeEmailRequest.kt
│   │   ├── OrderStatusUpdateEmailRequest.kt
│   │   └── EmailDeliveryStatusNotification.kt
│   └── payment           # Payment-related messaging DTOs
│       ├── PaymentLineItem.kt
│       ├── PaymentRequest.kt
│       ├── PaymentResponse.kt
│       └── PaymentNotification.kt