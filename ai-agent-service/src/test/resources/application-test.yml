spring:
  # Database configuration
  datasource:
    url: jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1
    username: sa
    password: sa
    driver-class-name: org.h2.Driver
  
  # JPA configuration
  jpa:
    hibernate:
      ddl-auto: create-drop
    database-platform: org.hibernate.dialect.H2Dialect
    show-sql: true
  
  # Disable RabbitMQ
  rabbitmq:
    listener:
      simple:
        auto-startup: false
  
  # Disable Flyway
  flyway:
    enabled: false

# Server configuration
server:
  port: 0

# Logging configuration
logging:
  level:
    org.springframework: INFO
    com.rj.ecommerce_ai_agent: DEBUG
    org.hibernate.SQL: DEBUG
