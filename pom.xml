<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <!-- Using Spring Boot Starter Parent for common dependency management -->
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>3.4.5</version> <!-- Use the version consistent with your modules -->
        <relativePath/> <!-- lookup parent from repository -->
    </parent>

    <groupId>com.rj</groupId>
    <artifactId>ecommerce-monorepo</artifactId>
    <version>1.0.0-SNAPSHOT</version>
    <packaging>pom</packaging> <!-- Essential for a multi-module parent -->

    <name>Ecommerce Monorepo</name>
    <description>Parent project for the Ecommerce Application</description>

    <properties>
        <!-- Keep Java and Kotlin versions consistent -->
        <java.version>21</java.version>
        <kotlin.version>1.9.25</kotlin.version>
        <!-- Define the version for your own modules -->
        <revision>${project.version}</revision>
    </properties>

    <modules>
        <module>shared-api</module>
        <module>backend</module>
        <module>ai-agent-service</module>
        <module>payment-service</module>
        <module>email-service</module>
    </modules>

    <dependencyManagement>
        <dependencies>
            <!-- Manage your internal project dependencies -->
            <dependency>
                <groupId>com.rj</groupId>
                <artifactId>shared-api</artifactId>
                <version>${revision}</version>
            </dependency>
            <!-- Add other shared dependencies to manage versions centrally if needed -->
            <dependency>
                <groupId>org.jetbrains.kotlin</groupId>
                <artifactId>kotlin-stdlib</artifactId>
                <version>${kotlin.version}</version>
            </dependency>
            <dependency>
                <groupId>org.jetbrains.kotlin</groupId>
                <artifactId>kotlin-reflect</artifactId>
                <version>${kotlin.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <pluginManagement>
            <plugins>
                <!-- Central management for Spring Boot plugin -->
                <plugin>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-maven-plugin</artifactId>
                    <!-- Version is inherited from spring-boot-starter-parent -->
                </plugin>
                <!-- Central management for Kotlin plugin -->
                <plugin>
                    <groupId>org.jetbrains.kotlin</groupId>
                    <artifactId>kotlin-maven-plugin</artifactId>
                    <version>${kotlin.version}</version>
                    <configuration>
                        <jvmTarget>${java.version}</jvmTarget>
                        <args>
                            <arg>-Xjsr305=strict</arg>
                        </args>
                        <compilerPlugins>
                            <plugin>spring</plugin>
                        </compilerPlugins>
                    </configuration>
                    <dependencies>
                        <dependency>
                            <groupId>org.jetbrains.kotlin</groupId>
                            <artifactId>kotlin-maven-allopen</artifactId>
                            <version>${kotlin.version}</version>
                        </dependency>
                    </dependencies>
                    <executions>
                        <execution>
                            <id>compile</id>
                            <phase>compile</phase>
                            <goals>
                                <goal>compile</goal>
                            </goals>
                        </execution>
                        <execution>
                            <id>test-compile</id>
                            <phase>test-compile</phase>
                            <goals>
                                <goal>test-compile</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
            </plugins>
        </pluginManagement>
        <plugins>
            <!-- Apply Kotlin plugin by default to all modules -->
            <plugin>
                <groupId>org.jetbrains.kotlin</groupId>
                <artifactId>kotlin-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>

    <profiles>
        <profile>
            <id>dev</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <properties>
                <spring.profiles.active>dev</spring.profiles.active>
            </properties>
        </profile>
        <profile>
            <id>local</id>
            <properties>
                <spring.profiles.active>local</spring.profiles.active>
            </properties>
        </profile>
        <profile>
            <id>container</id>
            <properties>
                <spring.profiles.active>container</spring.profiles.active>
            </properties>
        </profile>
        <profile>
            <id>prod</id>
            <properties>
                <spring.profiles.active>prod</spring.profiles.active>
            </properties>
        </profile>
    </profiles>

    <repositories>
        <repository>
            <id>spring-releases</id>
            <url>https://repo.spring.io/release</url>
        </repository>
        <repository>
            <id>maven-central</id>
            <url>https://repo.maven.apache.org/maven2</url>
        </repository>
    </repositories>

</project>