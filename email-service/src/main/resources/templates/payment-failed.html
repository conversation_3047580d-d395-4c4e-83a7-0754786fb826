<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment Failed</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            text-align: center;
            padding: 20px 0;
            border-bottom: 1px solid #eee;
        }
        .message-box {
            margin: 20px 0;
            padding: 15px;
            background-color: #fff4f4;
            border-left: 4px solid #ff6b6b;
            border-radius: 3px;
        }
        .order-details {
            margin: 20px 0;
            padding: 15px;
            background-color: #f9f9f9;
            border-radius: 5px;
        }
        .button {
            display: inline-block;
            padding: 10px 20px;
            background-color: #4285f4;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            margin: 10px 0;
        }
        .footer {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #eee;
            text-align: center;
            font-size: 12px;
            color: #777;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Payment Failed</h1>
    </div>

    <div class="message-box">
        <p>We were unable to process your payment for your recent order.</p>
    </div>

    <div class="order-details">
        <h2>Order Details</h2>
        <p><strong>Order Number:</strong> <span th:text="${orderId}">ORD-12345</span></p>
        <p th:if="${paymentAmount != null}"><strong>Amount:</strong> <span th:text="${paymentAmount != null ? (paymentAmount.amount + ' ' + paymentAmount.currencyCode) : ''}">$100.00</span></p>
    </div>

    <p>Don't worry! Your order is still saved, and you can complete your payment by clicking the button below:</p>

    <div th:if="${retryUrl != null}" style="text-align: center; margin: 20px 0;">
        <a th:href="${retryUrl}" class="button">Complete Payment</a>
    </div>

    <p>If you continue to experience issues, please contact our customer support team at <span th:text="${supportEmail != null ? supportEmail : '<EMAIL>'}"><EMAIL></span>.</p>

    <div class="footer">
        <p>Thank you for shopping with us!</p>
        <p>&copy; 2025 Your Ecommerce Store. All rights reserved.</p>
    </div>
</body>
</html>
