<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Order Refunded</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            text-align: center;
            padding: 20px 0;
            border-bottom: 1px solid #eee;
        }
        .logo {
            max-width: 150px;
            height: auto;
        }
        .message-box {
            margin: 20px 0;
            padding: 15px;
            background-color: #f0fff0;
            border-left: 4px solid #4CAF50;
            border-radius: 3px;
        }
        .order-details {
            margin: 20px 0;
            padding: 15px;
            background-color: #f9f9f9;
            border-radius: 5px;
        }
        .refund-details {
            margin: 20px 0;
            padding: 15px;
            background-color: #f0f7ff;
            border-radius: 5px;
        }
        .order-items {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        .order-items th, .order-items td {
            padding: 10px;
            border-bottom: 1px solid #ddd;
            text-align: left;
        }
        .order-items th {
            background-color: #f2f2f2;
        }
        .footer {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #eee;
            text-align: center;
            font-size: 12px;
            color: #777;
        }
        .help-section {
            margin: 20px 0;
            padding: 15px;
            background-color: #f0f7ff;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Refund Confirmation</h1>
    </div>

    <p>Hello <span th:text="${customer != null ? (customer.fullName() != null ? customer.fullName() : customer.email) : 'Valued Customer'}">Customer</span>,</p>

    <div class="message-box">
        <p>We're writing to confirm that your refund has been processed.</p>
        <p th:if="${additionalData != null && additionalData.refundReason != null}">
            <strong>Reason for refund:</strong> <span th:text="${additionalData.refundReason}">Return</span>
        </p>
    </div>

    <div class="order-details">
        <h2>Order Details</h2>
        <p><strong>Order Number:</strong> <span th:text="${orderNumber != null ? orderNumber : orderId}">ORD-12345</span></p>
        <p><strong>Order Date:</strong> <span th:text="${#temporals.format(orderDate, 'MMMM dd, yyyy')}">January 1, 2025</span></p>
    </div>

    <div class="refund-details">
        <h2>Refund Information</h2>
        <p><strong>Refund Amount:</strong> <span th:text="${additionalData != null && additionalData.refundAmount != null ? additionalData.refundAmount : totalAmount.formatted()}">$100.00</span></p>
        <p><strong>Refund Date:</strong> <span th:text="${#temporals.format(timestamp, 'MMMM dd, yyyy')}">January 5, 2025</span></p>
        <p><strong>Refund Method:</strong> <span th:text="${additionalData != null && additionalData.refundMethod != null ? additionalData.refundMethod : 'Original payment method'}">Original payment method</span></p>
        <p>Please note that it may take 5-10 business days for the refund to appear in your account, depending on your payment provider.</p>
    </div>

    <h2>Refunded Items</h2>
    <table class="order-items">
        <thead>
            <tr>
                <th>Product</th>
                <th>Quantity</th>
                <th>Price</th>
            </tr>
        </thead>
        <tbody>
            <tr th:each="item : ${items}">
                <td th:text="${item.productName}">Product Name</td>
                <td th:text="${item.quantity}">1</td>
                <td th:text="${item.unitPrice != null ? (item.unitPrice.amount + ' ' + item.unitPrice.currencyCode) : ''}">$10.00</td>
            </tr>
        </tbody>
    </table>

    <div class="help-section">
        <h2>Need Help?</h2>
        <p>If you have any questions about your refund, our customer service team is here to help.</p>
        <p>You can reply to this email or contact us through our website.</p>
    </div>

    <div class="footer">
        <p>We hope to serve you again soon!</p>
        <p>&copy; 2025 Your Ecommerce Store. All rights reserved.</p>
    </div>
</body>
</html>
