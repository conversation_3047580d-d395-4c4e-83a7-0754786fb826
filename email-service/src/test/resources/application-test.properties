# Test configuration for email service
spring.mail.host=localhost
spring.mail.port=3025
spring.mail.username=test
spring.mail.password=test
spring.mail.properties.mail.smtp.auth=false
spring.mail.properties.mail.smtp.starttls.enable=false

# Disable RabbitMQ for tests
spring.rabbitmq.listener.simple.auto-startup=false

# Thymeleaf configuration
spring.thymeleaf.cache=false
spring.thymeleaf.prefix=classpath:/email-templates/
spring.thymeleaf.suffix=.html

# Server configuration
server.port=0