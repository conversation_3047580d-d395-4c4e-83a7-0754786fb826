# Dockerfile_dev
FROM node:20-alpine as builder

# Install necessary build dependencies
RUN apk add --no-cache libc6-compat python3 make g++

# Set working directory
WORKDIR /app

# Copy package files FROM THE 'frontend' SUBDIRECTORY of the build context
COPY package*.json ./

# Install dependencies with current recommended settings
RUN npm cache clean --force && \
    npm config set legacy-peer-deps true && \
    npm install -g npm@latest && \
    npm install ajv@latest && \
    NODE_ENV=development npm install --no-package-lock --verbose && \
    npm install --save-dev @babel/plugin-proposal-private-property-in-object

# Copy project files FROM THE 'frontend' SUBDIRECTORY of the build context
COPY . .

# Build the application with explicit environment
ENV NODE_ENV=development
ENV DISABLE_ESLINT_PLUGIN=true

# Build the application
RUN npm run build

# Production stage
FROM node:20-alpine as runner
WORKDIR /app

# Copy built assets
COPY --from=builder /app/build ./build
COPY --from=builder /app/package*.json ./
COPY --from=builder /app/node_modules ./node_modules

# Expose port
EXPOSE 3000

# Start development server
CMD ["npm", "run", "start:docker"]