<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Redirecting to Stripe...</title>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
      background-color: #f7fafc;
      display: flex;
      justify-content: center;
      align-items: center;
      height: 100vh;
      margin: 0;
      padding: 20px;
    }
    .container {
      max-width: 500px;
      background-color: white;
      border-radius: 8px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
      padding: 24px;
      text-align: center;
    }
    h1 {
      color: #2d3748;
      margin-bottom: 16px;
    }
    p {
      color: #4a5568;
      margin-bottom: 24px;
    }
    .spinner {
      border: 4px solid rgba(0, 0, 0, 0.1);
      width: 36px;
      height: 36px;
      border-radius: 50%;
      border-left-color: #3b82f6;
      animation: spin 1s linear infinite;
      margin: 0 auto 24px;
    }
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
    .button {
      background-color: #3b82f6;
      color: white;
      border: none;
      padding: 12px 24px;
      border-radius: 4px;
      font-weight: 500;
      cursor: pointer;
      transition: background-color 0.2s;
      text-decoration: none;
      display: inline-block;
      margin-top: 16px;
    }
    .button:hover {
      background-color: #2563eb;
    }
    .debug {
      margin-top: 24px;
      padding: 16px;
      background-color: #f1f5f9;
      border-radius: 4px;
      text-align: left;
      font-size: 14px;
      color: #64748b;
    }
    .debug pre {
      white-space: pre-wrap;
      word-break: break-all;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>Redirecting to Stripe...</h1>
    <div class="spinner"></div>
    <p>You are being redirected to Stripe's secure payment page.</p>
    <p>If you are not redirected automatically, please click the button below:</p>

    <div id="manual-redirect">
      <a href="#" class="button" id="redirect-button">Continue to Payment</a>
    </div>

    <div class="debug">
      <strong>Debug Information:</strong>
      <pre id="debug-info">Loading...</pre>
    </div>
  </div>

  <script>
    // Get URL parameters
    const urlParams = new URLSearchParams(window.location.search);
    const sessionId = urlParams.get('session_id');
    const orderId = urlParams.get('order_id');

    // Update debug info
    document.getElementById('debug-info').textContent =
      `Session ID: ${sessionId || 'Not provided'}\nOrder ID: ${orderId || 'Not provided'}`;

    // Set up redirect URL
    let redirectUrl = '';
    if (sessionId) {
      // Check if the sessionId is a full URL or just an ID
      if (sessionId.startsWith('http')) {
        redirectUrl = sessionId;
      } else {
        redirectUrl = `https://checkout.stripe.com/c/pay/${sessionId}`;
      }

      document.getElementById('redirect-button').href = redirectUrl;
      document.getElementById('debug-info').textContent += `\n\nRedirect URL: ${redirectUrl}`;

      // Auto-redirect after a short delay
      setTimeout(() => {
        window.location.href = redirectUrl;
      }, 1500);
    } else {
      document.getElementById('debug-info').textContent += '\n\nERROR: No session ID provided. Cannot redirect.';
      document.getElementById('redirect-button').style.display = 'none';
    }
  </script>
</body>
</html>
