{"name": "ecommerce_frontend", "version": "0.1.0", "private": true, "dependencies": {"@heroicons/react": "^2.2.0", "@hookform/resolvers": "^3.9.1", "@mui/icons-material": "^5.15.11", "@reduxjs/toolkit": "^2.5.0", "@stripe/react-stripe-js": "^3.6.0", "@stripe/stripe-js": "^6.1.0", "@types/react": "^18.2.58", "@types/react-redux": "^7.1.34", "axios": "^1.7.9", "classnames": "^2.5.1", "cra-template-typescript": "1.2.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.54.1", "react-redux": "^9.2.0", "react-router-dom": "^6.22.2", "react-scripts": "5.0.1", "react-toastify": "^10.0.6", "tailwind-merge": "^2.6.0", "yup": "^1.5.0"}, "scripts": {"start": "react-scripts start", "start:docker": "WATCHPACK_POLLING=true react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "lint": "eslint 'src/**/*.{js,jsx,ts,tsx}'", "lint:fix": "eslint 'src/**/*.{js,jsx,ts,tsx}' --fix", "format": "prettier --write ."}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@babel/plugin-proposal-private-property-in-object": "^7.21.11", "@emotion/react": "^11.11.3", "@emotion/styled": "^11.11.0", "@mui/material": "^5.15.11", "@testing-library/jest-dom": "^6.4.2", "@testing-library/react": "^14.2.1", "@types/jest": "^29.5.12", "@types/lodash": "^4.14.202", "@types/lodash.debounce": "^4.0.9", "@types/react-dom": "^18.2.19", "autoprefixer": "^10.4.17", "postcss": "^8.4.35", "prettier": "^3.2.5", "tailwindcss": "^3.4.1", "typescript": "^4.9.5", "web-vitals": "^3.5.2"}}